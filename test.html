<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Date Picker Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .output {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .controls {
            margin: 10px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 8px 16px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }
        
        button:hover {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <h1>Advanced Date Picker Test Suite</h1>
    <p>This page tests all features of the advanced date picker component.</p>
    
    <div class="test-section">
        <h2>1. Single Date Selection</h2>
        <advanced-date-picker id="test1" selection="single"></advanced-date-picker>
        <div class="controls">
            <button onclick="setValue('test1', [{ date: '2024-01-15', times: ['09:00', '14:30'] }])">Set Value</button>
            <button onclick="getValue('test1')">Get Value</button>
            <button onclick="clearPicker('test1')">Clear</button>
        </div>
        <div id="test1-output" class="output"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Date Range Selection</h2>
        <advanced-date-picker id="test2" selection="range" months="2"></advanced-date-picker>
        <div class="controls">
            <button onclick="setValue('test2', [{ date: '2024-01-15', times: [] }, { date: '2024-01-20', times: [] }])">Set Range</button>
            <button onclick="getValue('test2')">Get Value</button>
            <button onclick="clearPicker('test2')">Clear</button>
        </div>
        <div id="test2-output" class="output"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Multiple Date Selection</h2>
        <advanced-date-picker id="test3" selection="multiple"></advanced-date-picker>
        <div class="controls">
            <button onclick="setValue('test3', [{ date: '2024-01-15', times: ['09:00'] }, { date: '2024-01-17', times: ['14:00'] }, { date: '2024-01-20', times: [] }])">Set Multiple</button>
            <button onclick="getValue('test3')">Get Value</button>
            <button onclick="clearPicker('test3')">Clear</button>
        </div>
        <div id="test3-output" class="output"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Inline Mode with Time Picker</h2>
        <advanced-date-picker id="test4" mode="inline" selection="single" time-format="12h" minute-step="15"></advanced-date-picker>
        <div class="controls">
            <button onclick="getValue('test4')">Get Value</button>
            <button onclick="clearPicker('test4')">Clear</button>
        </div>
        <div id="test4-output" class="output"></div>
    </div>
    
    <div class="test-section">
        <h2>5. Dark Theme & Localization</h2>
        <advanced-date-picker id="test5" theme="dark" selection="range" locale="en-GB" first-day-of-week="1"></advanced-date-picker>
        <div class="controls">
            <button onclick="getValue('test5')">Get Value</button>
            <button onclick="clearPicker('test5')">Clear</button>
        </div>
        <div id="test5-output" class="output"></div>
    </div>
    
    <div class="test-section">
        <h2>6. Constraints & Validation</h2>
        <advanced-date-picker id="test6" selection="single" min="2024-01-01" max="2024-12-31"></advanced-date-picker>
        <div class="controls">
            <button onclick="disableDates('test6')">Disable Holidays</button>
            <button onclick="getValue('test6')">Get Value</button>
            <button onclick="clearPicker('test6')">Clear</button>
        </div>
        <div id="test6-output" class="output"></div>
    </div>
    
    <div class="test-section">
        <h2>7. API Testing</h2>
        <advanced-date-picker id="test7" selection="single"></advanced-date-picker>
        <div class="controls">
            <button onclick="testAPI()">Run API Tests</button>
        </div>
        <div id="api-output" class="output"></div>
    </div>
    
    <script type="module" src="advanced-date-picker.js"></script>
    <script>
        // Wait for custom element to be defined
        customElements.whenDefined('advanced-date-picker').then(() => {
            setupEventListeners();
        });
        
        function setupEventListeners() {
            document.querySelectorAll('advanced-date-picker').forEach(picker => {
                const outputId = picker.id + '-output';
                const output = document.getElementById(outputId);
                
                picker.addEventListener('change', e => {
                    output.textContent = `Change: ${JSON.stringify(e.detail, null, 2)}`;
                });
                
                picker.addEventListener('open', e => {
                    console.log('Picker opened:', picker.id);
                });
                
                picker.addEventListener('close', e => {
                    console.log('Picker closed:', picker.id);
                });
                
                picker.addEventListener('validate', e => {
                    if (!e.detail.valid) {
                        output.textContent += `\nValidation Error: ${JSON.stringify(e.detail.errors, null, 2)}`;
                    }
                });
            });
        }
        
        function setValue(id, value) {
            const picker = document.getElementById(id);
            picker.setValue(value);
        }
        
        function getValue(id) {
            const picker = document.getElementById(id);
            const output = document.getElementById(id + '-output');
            const value = picker.getValue();
            output.textContent = `Value: ${JSON.stringify(value, null, 2)}`;
        }
        
        function clearPicker(id) {
            const picker = document.getElementById(id);
            picker.clear();
        }
        
        function disableDates(id) {
            const picker = document.getElementById(id);
            picker.disableDates(['2024-12-25', '2024-01-01', '2024-07-04']);
        }
        
        function testAPI() {
            const picker = document.getElementById('test7');
            const output = document.getElementById('api-output');
            let results = [];
            
            // Test properties
            results.push(`Initial selection mode: ${picker.selectionMode}`);
            picker.selectionMode = 'range';
            results.push(`Changed to range mode: ${picker.selectionMode}`);
            
            // Test locale
            results.push(`Initial locale: ${picker.locale}`);
            picker.locale = 'fr-FR';
            results.push(`Changed to French locale: ${picker.locale}`);
            
            // Test constraints
            picker.setMin('2024-06-01');
            picker.setMax('2024-06-30');
            results.push(`Set min/max constraints`);
            
            // Test disabled dates
            picker.disableDates(['2024-06-15', '2024-06-16']);
            results.push(`Disabled specific dates`);
            
            output.textContent = results.join('\n');
        }
    </script>
</body>
</html>

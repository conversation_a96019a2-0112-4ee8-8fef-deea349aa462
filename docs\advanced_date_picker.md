# advanced-date-picker

> **Single-file ES module & custom element prompt converted to Markdown**

This file contains a full specification, README, usage examples, API surface, accessibility and testing notes for a production-ready custom element `<advanced-date-picker>`.

---

## Overview

Build a production-ready advanced date & time picker as a single-file vanilla HTML/JS/CSS custom element named `<advanced-date-picker>`. Do not use any third-party libraries or frameworks. Deliver a single, self-contained ES module file that exports/defines the custom element so it can be used via:

```html
<script type="module" src="advanced-date-picker.js"></script>
```

### Goals

- Native Custom Element (`extends HTMLElement`) and registered as `advanced-date-picker`.
- Provide `inline` and `popup` modes (attribute `mode="inline"` or default popup).
- Support `single`, `range`, `multiple` selections and multiple time slots per date.
- Compact time-picker allowing multiple times per date (e.g., `09:00`, `14:30`).
- Keyboard navigation and ARIA roles for accessibility (WCAG 2.1 AA).
- Localizable month/day names and first-day-of-week option.
- Themeable via CSS variables; responsive and mobile-friendly.
- Everything inline and customizable via CSS variables.
- Robust JS API (properties, methods, events) for host apps.

---

## Quick usage example

```html
<!-- Minimal usage in HTML -->
<advanced-date-picker selection="range" months="2" locale="en-GB"></advanced-date-picker>

<script type="module">
  const dp = document.querySelector('advanced-date-picker');
  dp.addEventListener('change', e => console.log('selection', e.detail));
</script>
```

---

## Attributes & Configuration

These can be set as attributes on the element or via properties/options object.

- `selection` — string: `single` (default), `range`, `multiple`.
- `mode` — string: `popup` (default) or `inline`.
- `months` — integer: number of months to render side-by-side (default `1`).
- `locale` — string: BCP-47 locale id (default `navigator.language`).
- `first-day` — integer 0..6 (0=Sun default) — first day of week.
- `min` / `max` — ISO date string `YYYY-MM-DD` to limit date selection.
- `theme` — `light` / `dark` (default `light`).
- `time-format` — `24` or `12` (default `24`).
- `minute-step` — integer (default `15`).
- `preset-ranges` — JSON string or property to pass custom presets.
- `disabled-dates` — JSON string or `.disabledDates` property to pass disabled dates/ranges.

---

## Output format

The API returns structured selection as an array of objects:

```json
[
  { "date": "2025-09-24", "times": ["09:00", "14:30"] },
  { "date": "2025-09-26", "times": [] }
]
```

Times are stored as `HH:MM` strings (respecting configured 12/24 format when displaying but normalised to `HH:MM` for output).

---

## UI & Interaction

- Month grid view with weekday headers and `prev/next` controls.
- Jump-to-month/year UI (compact dropdowns or inputs).
- Multi-month layout controlled by `months` attribute.
- Today / Clear / Presets buttons.
- Hover preview for range selection; swapping when end < start.
- Visual indicators for dates that have time slots.
- Add/remove time rows for each selected date with validation.
- Disable specific dates or ranges programmatically.

---

## Accessibility (A11y)

- Use ARIA roles: `role="grid"` for calendar, `role="gridcell"` for day cells.
- Use `aria-selected`, `aria-disabled`, `aria-label` for screen reader clarity.
- Keyboard:
  - Arrow keys move focus between days.
  - Enter/Space selects.
  - Escape closes popup.
  - PageUp/PageDown changes month.
  - Home/End moves to first/last day of month.
- Focus management: trap focus while popup is open, restore to trigger on close.

---

## Events

Custom events emitted on the host element (use `event.detail` for payloads):

- `change` — fired whenever selection changes. `detail` contains structured selection array.
- `open` / `close` — popup opened/closed.
- `validate` — run when validation occurs. `detail` contains `{ pass: boolean, reason?: string }`.
- `error` — when user attempts an invalid action (e.g., selecting disabled date).

Events are `bubbles: true, composed: true` so they can be listened to from host pages.

---

## Methods & Public API

Available on the element instance:

- `.open()` — open popup (no-op if `mode="inline"`).
- `.close()` — close popup.
- `.toggle()` — toggle popup.
- `.getValue()` → returns structured JSON value (dates + times).
- `.setValue(value)` → accepts the same structure and updates UI.
- `.clear()` → clears selection.
- `.disableDates(datesArray)` → disables provided dates/ranges.
- `.enableDates(datesArray)` → enables provided dates/ranges.
- `.setMin(dateString)` / `.setMax(dateString)` — set min/max.
- `.validate()` — run validation and emit `validate` event with result.

Properties mirror key attributes for JS convenience: `selectionMode`, `locale`, `min`, `max`, `disabledDates`, `months`.

---

## Validation & Constraints

- Selections outside min/max are prevented. Attempting triggers `validate` event with `pass:false` and an accessible message.
- Time slots tied to dates must respect min/max when a date includes time information (e.g., min `2025-09-24T10:00`).
- Visual error state with `aria-live` region for screen readers.

---

## Styling & Theming

Expose CSS variables (defaults shown):

```css
--adp-bg: #ffffff;
--adp-fg: #111827;
--adp-accent: #2563eb;
--adp-accent-contrast: #ffffff;
--adp-border: #e5e7eb;
--adp-radius: 8px;
--adp-shadow: 0 6px 18px rgba(15, 15, 15, 0.08);
```

Support `theme="dark"` attribute to flip sensible defaults.

---

## Performance & Implementation Notes

- Use `Set` for `disabledDates` lookups for O(1) checks.
- Render only visible months; use document fragments for batch updates.
- Debounce heavy operations (e.g., large disabledDates updates).
- Use `requestAnimationFrame` for visual updates/animations.
- Separate state, rendering, and DOM helpers for maintainability.

---

## Built-in Demo (when opened directly)

If the JS file is opened directly (e.g., loaded as a script on a page that includes the module file directly), the demo should render a small demo UI showcasing:

- Single, range and multiple selection modes.
- Adding multiple times to a date.
- Presets, setting and clearing.
- Keyboard navigation demo.

The demo is minimal and for manual testing only.

---

## Test cases (for automated tester) — small set included as comments

1. **Keyboard navigation**: Arrow Right moves to next day; Enter selects that day.
2. **Range selection**: Click start day, Shift+Click end day selects full range.
3. **Multi-time**: Add two times to a date, call `.getValue()` and assert date object contains both times.
4. **Disabled dates**: Add a disabled date; attempting to select it triggers `validate` event with `{ pass: false }` and the date remains unselected.
5. **Min/Max**: Dates outside min/max cannot be selected and produce `validate` failure.

---

## Implementation plan (developer notes)

Break the implementation into modules inside the single file:

- **Constants & helpers** — date math, formatting via `Intl.DateTimeFormat`, small utilities.
- **State management** — internal state object, setters & getters, change emitter.
- **DOM rendering** — calendar grid, controls, time-picker rows.
- **Accessibility & keyboard** — focus trap, key handlers.
- **API wiring** — public methods, event emissions.
- **Persistence helpers** — optional (e.g., `localStorage`) for demo only.

Add thorough inline comments explaining major functions and public API.

---

## README block (summary of attributes, methods, events, defaults)

### Attributes

- `selection` — `single` | `range` | `multiple` (default `single`).
- `mode` — `popup` | `inline` (default `popup`).
- `months` — number of months to show side-by-side (default `1`).
- `locale` — BCP-47 locale (default `navigator.language`).
- `first-day` — 0..6 (default `0`).
- `min`, `max` — ISO `YYYY-MM-DD` strings.
- `theme` — `light` | `dark`.
- `time-format` — `24` | `12`.
- `minute-step` — number (default `15`).

### Methods

- `.open()`, `.close()`, `.toggle()`
- `.getValue()`, `.setValue(value)`, `.clear()`
- `.disableDates(arr)`, `.enableDates(arr)`
- `.setMin(date)`, `.setMax(date)`

### Events

- `change` — selection changed.
- `open` / `close` — popup lifecycle.
- `validate` — validation results.
- `error` — invalid attempts.

### Defaults

- `selection: "single"`
- `mode: "popup"`
- `months: 1`
- `locale: navigator.language`
- `first-day: 0`
- `time-format: 24`
- `minute-step: 15`

---

_End of specification. Implement the single ES module file according to the above._


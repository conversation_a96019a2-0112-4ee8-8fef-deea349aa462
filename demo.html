<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Date Picker Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        
        .demo-section {
            background: white;
            margin: 20px 0;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .demo-section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .picker-container {
            margin: 20px 0;
        }
        
        .output {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .controls {
            margin: 15px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid #007bff;
            border-radius: 6px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .btn:hover {
            background: #0056b3;
            border-color: #0056b3;
        }
        
        .btn-secondary {
            background: white;
            color: #007bff;
        }
        
        .btn-secondary:hover {
            background: #f8f9fa;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .feature-item {
            padding: 15px;
            background: #e3f2fd;
            border-radius: 6px;
            border-left: 4px solid #2196f3;
        }
        
        .hero {
            text-align: center;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        
        .hero h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .hero p {
            margin: 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="hero">
        <h1>🗓️ Advanced Date Picker</h1>
        <p>A comprehensive, accessible, and customizable date & time picker component</p>
    </div>
    
    <div class="demo-section">
        <h2>✨ Key Features</h2>
        <div class="feature-list">
            <div class="feature-item">
                <strong>Multiple Selection Modes</strong><br>
                Single date, date ranges, and multiple dates
            </div>
            <div class="feature-item">
                <strong>Time Picker Integration</strong><br>
                Add multiple time slots per date with 12h/24h formats
            </div>
            <div class="feature-item">
                <strong>Accessibility First</strong><br>
                Full keyboard navigation and ARIA support
            </div>
            <div class="feature-item">
                <strong>Internationalization</strong><br>
                Locale support with customizable first day of week
            </div>
            <div class="feature-item">
                <strong>Theming & Responsive</strong><br>
                CSS variables for theming, mobile-friendly design
            </div>
            <div class="feature-item">
                <strong>Validation & Constraints</strong><br>
                Min/max dates, disabled dates, and error handling
            </div>
        </div>
    </div>
    
    <div class="demo-section">
        <h2>📅 Single Date Selection</h2>
        <div class="picker-container">
            <advanced-date-picker id="single" selection="single"></advanced-date-picker>
        </div>
        <div class="controls">
            <button class="btn" onclick="setValue('single', [{ date: '2024-01-15', times: ['09:00', '14:30'] }])">Set Sample Date</button>
            <button class="btn btn-secondary" onclick="clearPicker('single')">Clear</button>
        </div>
        <div id="single-output" class="output">Select a date to see the output...</div>
    </div>
    
    <div class="demo-section">
        <h2>📊 Date Range Selection</h2>
        <div class="picker-container">
            <advanced-date-picker id="range" selection="range" months="2"></advanced-date-picker>
        </div>
        <div class="controls">
            <button class="btn" onclick="setValue('range', [{ date: '2024-01-15', times: [] }, { date: '2024-01-20', times: [] }])">Set Sample Range</button>
            <button class="btn btn-secondary" onclick="clearPicker('range')">Clear</button>
        </div>
        <div id="range-output" class="output">Select a date range to see the output...</div>
    </div>
    
    <div class="demo-section">
        <h2>📋 Multiple Date Selection</h2>
        <div class="picker-container">
            <advanced-date-picker id="multiple" selection="multiple"></advanced-date-picker>
        </div>
        <div class="controls">
            <button class="btn" onclick="setValue('multiple', [{ date: '2024-01-15', times: ['09:00'] }, { date: '2024-01-17', times: ['14:00'] }, { date: '2024-01-20', times: [] }])">Set Sample Dates</button>
            <button class="btn btn-secondary" onclick="clearPicker('multiple')">Clear</button>
        </div>
        <div id="multiple-output" class="output">Select multiple dates to see the output...</div>
    </div>
    
    <div class="demo-section">
        <h2>⏰ Inline Mode with Time Picker</h2>
        <div class="picker-container">
            <advanced-date-picker id="inline" mode="inline" selection="single" time-format="12h" minute-step="15"></advanced-date-picker>
        </div>
        <div class="controls">
            <button class="btn btn-secondary" onclick="clearPicker('inline')">Clear</button>
        </div>
        <div id="inline-output" class="output">Select a date and add times to see the output...</div>
    </div>
    
    <div class="demo-section">
        <h2>🌙 Dark Theme & Localization</h2>
        <div class="picker-container">
            <advanced-date-picker id="dark" theme="dark" selection="range" locale="en-GB" first-day-of-week="1"></advanced-date-picker>
        </div>
        <div class="controls">
            <button class="btn btn-secondary" onclick="clearPicker('dark')">Clear</button>
        </div>
        <div id="dark-output" class="output">Dark theme with UK locale (Monday first)...</div>
    </div>
    
    <script type="module" src="advanced-date-picker.js"></script>
    <script>
        // Wait for custom element to be defined
        customElements.whenDefined('advanced-date-picker').then(() => {
            setupEventListeners();
        });
        
        function setupEventListeners() {
            document.querySelectorAll('advanced-date-picker').forEach(picker => {
                const outputId = picker.id + '-output';
                const output = document.getElementById(outputId);
                
                picker.addEventListener('change', e => {
                    output.textContent = JSON.stringify(e.detail, null, 2);
                });
                
                picker.addEventListener('validate', e => {
                    if (!e.detail.valid) {
                        output.textContent += `\n\nValidation Errors:\n${JSON.stringify(e.detail.errors, null, 2)}`;
                    }
                });
            });
        }
        
        function setValue(id, value) {
            const picker = document.getElementById(id);
            picker.setValue(value);
        }
        
        function clearPicker(id) {
            const picker = document.getElementById(id);
            picker.clear();
        }
    </script>
</body>
</html>

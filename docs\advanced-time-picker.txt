Build a production-ready advanced time picker as a single-file vanilla HTML/JS/CSS custom element named <advanced-time-picker>.

Deliver one self-contained ES module file (e.g., advanced-time-picker.js) that exports/defines the custom element so it can be used via:

<script type="module" src="advanced-time-picker.js"></script>

Goals & Overview

Implement <advanced-time-picker> as a native Custom Element (HTMLElement) and register as advanced-time-picker.

Support 12h and 24h formats (via format="12" or format="24", default 24).

Configurable minute step (default 15, attribute step="5|10|15|30").

Support single time, multiple times per instance.

UI to add/remove times dynamically.

Expose robust JS API (properties, methods, events) for host apps.

Accessible (ARIA roles, keyboard navigation, WCAG 2.1 AA).

Localizable (AM/PM strings, accessible labels).

Themeable with CSS variables, fully responsive.

No external libraries, styles, or assets.

Required Features

Time selection modes

Default: select one time.

With multiple attribute: allow multiple time slots.

Keyboard navigation

Arrow Up/Down: increment/decrement hours/minutes.

Tab: move between fields.

Enter: confirm selection.

Delete/Backspace: remove focused time slot.

JS API
Methods:

.getValue() → returns array of times as strings (["09:00", "14:30"]).

.setValue(timesArray) → sets times.

.clear() → removes all times.

.addTime("HH:MM") → programmatically add a time.

Events:

change → fired when times change, detail includes current values.

validate → when validation runs (contains pass/fail).

Styling & Theming

CSS variables:

--atp-bg, --atp-fg, --atp-accent, --atp-radius, --atp-border, --atp-shadow.

Responsive layout for mobile/desktop.

Accessibility

Use ARIA roles for list/grid.

Manage focus when multiple time slots are active.

Documentation & Demo

Include file header README with usage example:

<advanced-time-picker multiple format="12" step="15"></advanced-time-picker>

<script type="module">
  const tp = document.querySelector('advanced-time-picker');
  tp.addEventListener('change', e => console.log(e.detail));
</script>


If opened directly in the browser (via document.currentScript), show a built-in demo with:

Single mode.

Multiple mode.

12h vs 24h format.

Adding/removing times.

Test Cases (as comments):

Arrow up/down increments/decrements correctly.

Adding/removing times updates .getValue().

12h/24h formats output correct strings.

Step=30 restricts minutes to multiples of 30.
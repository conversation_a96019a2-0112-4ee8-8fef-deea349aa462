Prompt to generate an advanced date/time picker (custom element)

Build a production-ready advanced date & time picker as a single-file vanilla HTML/JS/CSS custom element named <advanced-date-picker>. Do not use any third-party libraries or frameworks. Deliver a single, self-contained ES module file that exports/defines the custom element so it can be used via <script type="module" src="advanced-date-picker.js"></script>.

Goals & Overview

Implement <advanced-date-picker> as a native Custom Element (extends HTMLElement) and register as advanced-date-picker.

Provide both inline and popup modes (attribute mode="inline" or default popup).

Support single-date, date-range, multi-date, and multiple time slots per date selection.

Include a compact time-picker that allows adding multiple times for the same date (e.g., 09:00, 14:30).

Provide keyboard navigation and ARIA roles for accessibility (WCAG 2.1 AA).

Localizable (i18n) month/day names and first-day-of-week option.

Themeable via CSS variables; layout responsive and mobile-friendly.

No external stylesheets or images — everything inline and customizable via CSS variables.

Expose a robust JS API (properties, methods, events) for host apps.

Required Features & Behavior
Selection modes (via mode / selection attributes / option object)

selection="single" — pick one date (with optional time).

selection="range" — pick start & end dates; support inverted selections (end before start should swap).

selection="multiple" — pick any number of dates.

For each date, support adding multiple time slots (like meetings) — user can add/remove time rows per date.

Time and slots

Time picker supports 24h and 12h (AM/PM) formats (configurable).

Allow minute-step configuration (e.g., 5, 15).

Allow preset time sets per date (e.g., "Morning: 09:00, 11:00").

UI to add/remove times for a selected date.

API should return selected dates as ISO strings with time array for each date:

[
  { "date": "2025-09-24", "times": ["09:00", "14:30"] },
  { "date": "2025-09-26", "times": [] }
]

Navigation & UI

Month view with weekdays header; show current month, prev/next controls, jump-to-month/year UI.

Support multi-month view (attribute months="2").

Today button and Clear button.

Optional min/max date attributes.

Ability to disable specific dates or date ranges (via attribute or .disabledDates = [...]).

Range highlight styling, and visual indicator for dates with times.

Hover preview for range selection (show prospective range while hovering).

Preset ranges (Today, Next 7 days, This Month) and ability to pass custom presets.

Accessibility & Keyboard

Use ARIA roles (grid, gridcell, etc.) and aria-selected, aria-disabled.

Keyboard support: arrow keys to move, Enter/Space to select, Escape to close, PageUp/PageDown to change month, Home/End to go to first/last day of month.

Focus management: trap focus when popup open, restore focus on close.

Localization & formatting

Accept locale attribute or option (e.g., locale="en-GB").

Accept first-day-of-week config (0=Sun..6=Sat).

Date/time formatting uses Intl.DateTimeFormat where possible.

Events & API

Fire custom events:

change — when selection changes, detail contains structured selection.

open / close — when popup opens or closes.

validate — when validation runs (contains pass/fail).

Expose methods:

.open(), .close(), .toggle()

.getValue() → returns structured JSON (dates + times)

.setValue(value) → accepts same structure and updates UI

.clear()

.disableDates(datesArray) / .enableDates(datesArray)

.setMin(dateString) / .setMax(dateString)

Expose properties: selectionMode, locale, min, max, disabledDates, months.

Validation & Constraints

Validate selected times against min/max date-times.

Provide callback or event when user attempts invalid selection.

Provide visual error states and accessible error messages.

Styling & Theming

Use CSS variables for colors, spacing, fonts, radii, shadows:

--adp-bg, --adp-fg, --adp-accent, --adp-accent-contrast, --adp-border, --adp-radius, --adp-shadow.

Provide light/dark toggle (via attribute theme="dark").

Responsive layout: stacked on small screens, grid of months on larger screens.

Performance & Large Data

Efficient rendering for multi-month views and many disabled dates (use Set lookups).

Debounce heavy operations; use requestAnimationFrame for animations.

Developer Experience & Documentation

Provide a minimal usage example in the file header:

<advanced-date-picker selection="range" months="2" locale="en-GB"></advanced-date-picker>

<script type="module">
  const dp = document.querySelector('advanced-date-picker');
  dp.addEventListener('change', e => console.log('selection', e.detail));
</script>


Provide inline code comments explaining major functions and public API.

Include small README block at top of file explaining attributes, methods, events, and default values.

Testing & Acceptance Criteria

Include a simple built-in demo UI when file is opened directly in browser (e.g., if document.currentScript used), showing:

Single, range, multiple selections;

Adding multiple times to a date;

Setting and clearing presets;

Keyboard navigation demonstration.

Provide a small set of test cases (as comments) that an automated tester could perform:

Keyboard navigation: Arrow Right moves to next day; Enter selects.

Range selection: click start, shift+click end selects range.

Multi-time: add two times to a date, ensure both saved in .getValue().

Disabled dates: attempting to select disabled date triggers validate event with allowed=false.

Min/Max: dates outside min/max cannot be selected.

Output format & constraints

Output exactly one ES module file (no external assets).

Keep code well-structured: small helper functions, clear separation of DOM, state, and rendering logic.

Use modern JS (ES2020+ features allowed), but avoid transpilation-specific features that break native modules.

Comment major blocks and exported API thoroughly.
/**
 * Advanced Date & Time Picker Custom Element
 * 
 * A production-ready advanced date & time picker as a single-file vanilla HTML/JS/CSS custom element.
 * 
 * Usage:
 * <advanced-date-picker selection="range" months="2" locale="en-GB"></advanced-date-picker>
 * 
 * <script type="module">
 *   const dp = document.querySelector('advanced-date-picker');
 *   dp.addEventListener('change', e => console.log('selection', e.detail));
 * </script>
 * 
 * Attributes:
 * - selection: "single" | "range" | "multiple" (default: "single")
 * - mode: "popup" | "inline" (default: "popup")
 * - months: number of months to display (default: 1)
 * - locale: locale string (default: "en-US")
 * - theme: "light" | "dark" (default: "light")
 * - min: minimum date (ISO string)
 * - max: maximum date (ISO string)
 * - first-day-of-week: 0-6 (0=Sunday, default: 0)
 * - time-format: "12h" | "24h" (default: "24h")
 * - minute-step: minute increment (default: 15)
 * 
 * Methods:
 * - open(), close(), toggle()
 * - getValue() → returns structured JSON (dates + times)
 * - setValue(value) → accepts same structure and updates UI
 * - clear()
 * - disableDates(datesArray) / enableDates(datesArray)
 * - setMin(dateString) / setMax(dateString)
 * 
 * Events:
 * - change: when selection changes
 * - open/close: when popup opens or closes
 * - validate: when validation runs
 */

class AdvancedDatePicker extends HTMLElement {
    constructor() {
        super();

        // Initialize state
        this.state = {
            isOpen: false,
            currentMonth: new Date(),
            selectedDates: [],
            hoveredDate: null,
            focusedDate: new Date(),
            disabledDates: new Set(),
            timeSlots: new Map() // Map<dateString, timeArray>
        };

        // Configuration
        this.config = {
            selection: 'single',
            mode: 'popup',
            months: 1,
            locale: 'en-US',
            theme: 'light',
            min: null,
            max: null,
            firstDayOfWeek: 0,
            timeFormat: '24h',
            minuteStep: 15
        };

        // Create shadow DOM
        this.attachShadow({ mode: 'open' });

        // Bind methods
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.handleDocumentClick = this.handleDocumentClick.bind(this);
        this.handleDateClick = this.handleDateClick.bind(this);
        this.handleDateHover = this.handleDateHover.bind(this);
        this.handleTimeAdd = this.handleTimeAdd.bind(this);
        this.handleTimeRemove = this.handleTimeRemove.bind(this);

        // Initialize component
        this.init();
    }

    static get observedAttributes() {
        return [
            'selection', 'mode', 'months', 'locale', 'theme',
            'min', 'max', 'first-day-of-week', 'time-format', 'minute-step'
        ];
    }

    attributeChangedCallback(name, oldValue, newValue) {
        if (oldValue === newValue) return;

        const configKey = name.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());

        switch (name) {
            case 'months':
            case 'first-day-of-week':
            case 'minute-step':
                this.config[configKey] = parseInt(newValue) || this.config[configKey];
                break;
            case 'min':
            case 'max':
                this.config[configKey] = newValue ? new Date(newValue) : null;
                break;
            default:
                this.config[configKey] = newValue || this.config[configKey];
        }

        if (this.shadowRoot.innerHTML) {
            this.render();
        }
    }

    connectedCallback() {
        this.updateConfigFromAttributes();
        this.render();
        this.attachEventListeners();
    }

    disconnectedCallback() {
        this.detachEventListeners();
    }

    init() {
        // Initialize current month to today
        this.state.currentMonth = new Date();
        this.state.focusedDate = new Date();
    }

    updateConfigFromAttributes() {
        // Update config from attributes
        const attrs = this.constructor.observedAttributes;
        attrs.forEach(attr => {
            if (this.hasAttribute(attr)) {
                this.attributeChangedCallback(attr, null, this.getAttribute(attr));
            }
        });
    }

    attachEventListeners() {
        if (this.config.mode === 'popup') {
            document.addEventListener('click', this.handleDocumentClick);
        }
        document.addEventListener('keydown', this.handleKeyDown);
    }

    detachEventListeners() {
        document.removeEventListener('click', this.handleDocumentClick);
        document.removeEventListener('keydown', this.handleKeyDown);
    }

    render() {
        this.shadowRoot.innerHTML = `
            ${this.getStyles()}
            <div class="adp-container" data-theme="${this.config.theme}">
                ${this.config.mode === 'popup' ? this.renderTrigger() : ''}
                <div class="adp-picker ${this.config.mode === 'popup' ? 'adp-popup' : 'adp-inline'}"
                     role="dialog"
                     aria-label="Date picker"
                     aria-modal="${this.config.mode === 'popup' ? 'true' : 'false'}"
                     ${this.config.mode === 'popup' ? `style="display: ${this.state.isOpen ? 'block' : 'none'}"` : ''}>
                    ${this.renderHeader()}
                    ${this.renderCalendar()}
                    ${this.renderTimePanel()}
                    ${this.renderFooter()}
                </div>
            </div>
        `;

        this.attachPickerEventListeners();
    }

    renderTrigger() {
        const value = this.getDisplayValue();
        return `
            <button class="adp-trigger"
                    type="button"
                    aria-haspopup="dialog"
                    aria-expanded="${this.state.isOpen}"
                    aria-label="Open date picker. ${value ? `Current selection: ${value}` : 'No date selected'}">
                <span class="adp-trigger-text">${value || 'Select date...'}</span>
                <span class="adp-trigger-icon" aria-hidden="true">📅</span>
            </button>
        `;
    }

    getDisplayValue() {
        if (this.state.selectedDates.length === 0) return '';

        const formatter = new Intl.DateTimeFormat(this.config.locale, {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });

        if (this.config.selection === 'single') {
            return formatter.format(this.state.selectedDates[0]);
        } else if (this.config.selection === 'range' && this.state.selectedDates.length === 2) {
            return `${formatter.format(this.state.selectedDates[0])} - ${formatter.format(this.state.selectedDates[1])}`;
        } else if (this.config.selection === 'multiple') {
            return `${this.state.selectedDates.length} dates selected`;
        }

        return '';
    }

    handleKeyDown(e) {
        if (!this.state.isOpen && this.config.mode === 'popup') return;

        const focusedCell = this.shadowRoot.querySelector('.adp-day-focused');
        if (!focusedCell) return;

        const currentDate = new Date(this.state.focusedDate);
        let newDate = new Date(currentDate);

        switch (e.key) {
            case 'ArrowLeft':
                newDate.setDate(newDate.getDate() - 1);
                e.preventDefault();
                break;
            case 'ArrowRight':
                newDate.setDate(newDate.getDate() + 1);
                e.preventDefault();
                break;
            case 'ArrowUp':
                newDate.setDate(newDate.getDate() - 7);
                e.preventDefault();
                break;
            case 'ArrowDown':
                newDate.setDate(newDate.getDate() + 7);
                e.preventDefault();
                break;
            case 'Home':
                newDate.setDate(1);
                e.preventDefault();
                break;
            case 'End':
                newDate.setMonth(newDate.getMonth() + 1, 0);
                e.preventDefault();
                break;
            case 'PageUp':
                newDate.setMonth(newDate.getMonth() - (e.shiftKey ? 12 : 1));
                e.preventDefault();
                break;
            case 'PageDown':
                newDate.setMonth(newDate.getMonth() + (e.shiftKey ? 12 : 1));
                e.preventDefault();
                break;
            case 'Enter':
            case ' ':
                this.selectDate(currentDate);
                e.preventDefault();
                break;
            case 'Escape':
                if (this.config.mode === 'popup') {
                    this.close();
                }
                e.preventDefault();
                break;
        }

        if (newDate.getTime() !== currentDate.getTime()) {
            this.setFocusedDate(newDate);
        }
    }

    handleDocumentClick(e) {
        if (this.config.mode === 'popup' && !this.contains(e.target)) {
            this.close();
        }
    }

    handleDateClick(e) {
        const dayBtn = e.target.closest('.adp-day-btn');
        if (!dayBtn) return;

        const cell = dayBtn.closest('.adp-day');
        const dateStr = cell.dataset.date;
        const date = new Date(dateStr + 'T00:00:00');

        if (this.isDateDisabled(date)) {
            const reason = this.getDisabledReason(date);
            this.dispatchEvent(new CustomEvent('validate', {
                detail: { date: dateStr, allowed: false, reason }
            }));
            this.showValidationError(reason);
            return;
        }

        this.selectDate(date);
        this.setFocusedDate(date);
    }

    handleDateHover(e) {
        const dayBtn = e.target.closest('.adp-day-btn');
        if (!dayBtn) {
            this.state.hoveredDate = null;
            this.render();
            return;
        }

        const cell = dayBtn.closest('.adp-day');
        const dateStr = cell.dataset.date;
        const date = new Date(dateStr + 'T00:00:00');

        if (this.config.selection === 'range' && this.state.selectedDates.length === 1) {
            this.state.hoveredDate = date;
            this.render();
        }
    }

    selectDate(date) {
        const dateStr = date.toISOString().split('T')[0];

        switch (this.config.selection) {
            case 'single':
                this.state.selectedDates = [new Date(date)];
                break;

            case 'range':
                if (this.state.selectedDates.length === 0) {
                    this.state.selectedDates = [new Date(date)];
                } else if (this.state.selectedDates.length === 1) {
                    const firstDate = this.state.selectedDates[0];
                    this.state.selectedDates = [firstDate, new Date(date)].sort((a, b) => a - b);
                } else {
                    // Start new range
                    this.state.selectedDates = [new Date(date)];
                }
                break;

            case 'multiple':
                const existingIndex = this.state.selectedDates.findIndex(d => this.isSameDay(d, date));
                if (existingIndex >= 0) {
                    // Remove if already selected
                    this.state.selectedDates.splice(existingIndex, 1);
                    this.state.timeSlots.delete(dateStr);
                } else {
                    // Add to selection
                    this.state.selectedDates.push(new Date(date));
                }
                break;
        }

        this.state.hoveredDate = null;
        this.render();
        this.dispatchEvent(new CustomEvent('change', { detail: this.getValue() }));
    }

    setFocusedDate(date) {
        this.state.focusedDate = new Date(date);

        // Update current month if focused date is outside current view
        const currentMonth = this.state.currentMonth;
        if (date.getMonth() !== currentMonth.getMonth() ||
            date.getFullYear() !== currentMonth.getFullYear()) {
            this.state.currentMonth = new Date(date.getFullYear(), date.getMonth(), 1);
        }

        this.render();

        // Focus the new date cell
        requestAnimationFrame(() => {
            const focusedCell = this.shadowRoot.querySelector('.adp-day-focused .adp-day-btn');
            if (focusedCell) {
                focusedCell.focus();
            }
        });
    }

    handleTimeAdd() {
        const timeInput = this.shadowRoot.querySelector('.adp-time-input');
        timeInput.style.display = timeInput.style.display === 'none' ? 'block' : 'none';

        if (timeInput.style.display === 'block') {
            const hourSelect = timeInput.querySelector('.adp-hour-select');
            hourSelect.focus();
        }
    }

    handleTimeRemove(e) {
        const timeSlot = e.target.closest('.adp-time-slot');
        const dateStr = timeSlot.dataset.date;
        const index = parseInt(timeSlot.dataset.index);

        const times = this.state.timeSlots.get(dateStr) || [];
        times.splice(index, 1);

        if (times.length === 0) {
            this.state.timeSlots.delete(dateStr);
        } else {
            this.state.timeSlots.set(dateStr, times);
        }

        this.render();
        this.dispatchEvent(new CustomEvent('change', { detail: this.getValue() }));
    }

    attachPickerEventListeners() {
        const picker = this.shadowRoot.querySelector('.adp-picker');
        if (!picker) return;

        // Trigger button (popup mode)
        const trigger = this.shadowRoot.querySelector('.adp-trigger');
        if (trigger) {
            trigger.addEventListener('click', () => this.toggle());
        }

        // Navigation buttons
        picker.querySelector('.adp-prev-year')?.addEventListener('click', () => this.navigateYear(-1));
        picker.querySelector('.adp-prev-month')?.addEventListener('click', () => this.navigateMonth(-1));
        picker.querySelector('.adp-next-month')?.addEventListener('click', () => this.navigateMonth(1));
        picker.querySelector('.adp-next-year')?.addEventListener('click', () => this.navigateYear(1));

        // Date cells
        picker.addEventListener('click', (e) => {
            if (e.target.closest('.adp-day-btn')) {
                this.handleDateClick(e);
            }
        });

        picker.addEventListener('mouseover', (e) => this.handleDateHover(e));
        picker.addEventListener('mouseout', (e) => this.handleDateHover(e));

        // Time controls
        picker.querySelector('.adp-time-add-btn')?.addEventListener('click', () => this.handleTimeAdd());
        picker.addEventListener('click', (e) => {
            if (e.target.classList.contains('adp-time-remove-btn')) {
                this.handleTimeRemove(e);
            }
        });

        // Time input controls
        const timeSaveBtn = picker.querySelector('.adp-time-save-btn');
        const timeCancelBtn = picker.querySelector('.adp-time-cancel-btn');

        timeSaveBtn?.addEventListener('click', () => this.saveTimeInput());
        timeCancelBtn?.addEventListener('click', () => this.cancelTimeInput());

        // Footer buttons
        picker.querySelector('.adp-today-btn')?.addEventListener('click', () => this.goToToday());
        picker.querySelector('.adp-clear-btn')?.addEventListener('click', () => this.clear());
        picker.querySelector('.adp-close-btn')?.addEventListener('click', () => this.close());

        // Preset buttons
        picker.addEventListener('click', (e) => {
            if (e.target.classList.contains('adp-preset-btn')) {
                this.handlePreset(e.target.dataset.preset);
            }
        });
    }

    renderHeader() {
        const currentDate = new Date(this.state.currentMonth);
        const monthName = currentDate.toLocaleDateString(this.config.locale, { month: 'long', year: 'numeric' });

        return `
            <div class="adp-header">
                <div class="adp-nav">
                    <button class="adp-nav-btn adp-prev-year" type="button" aria-label="Previous year">‹‹</button>
                    <button class="adp-nav-btn adp-prev-month" type="button" aria-label="Previous month">‹</button>
                    <div class="adp-month-year">
                        <button class="adp-month-btn" type="button">${monthName}</button>
                    </div>
                    <button class="adp-nav-btn adp-next-month" type="button" aria-label="Next month">›</button>
                    <button class="adp-nav-btn adp-next-year" type="button" aria-label="Next year">››</button>
                </div>
                <div class="adp-presets">
                    <button class="adp-preset-btn" type="button" data-preset="today">Today</button>
                    <button class="adp-preset-btn" type="button" data-preset="week">Next 7 days</button>
                    <button class="adp-preset-btn" type="button" data-preset="month">This Month</button>
                </div>
            </div>
        `;
    }

    renderCalendar() {
        const months = [];
        for (let i = 0; i < this.config.months; i++) {
            const monthDate = new Date(this.state.currentMonth);
            monthDate.setMonth(monthDate.getMonth() + i);
            months.push(this.renderMonth(monthDate));
        }

        return `
            <div class="adp-calendar" role="grid" aria-label="Calendar">
                ${months.join('')}
            </div>
        `;
    }

    renderMonth(monthDate) {
        const year = monthDate.getFullYear();
        const month = monthDate.getMonth();
        const firstDay = new Date(year, month, 1);

        const startDate = new Date(firstDay);

        // Adjust start date to first day of week
        const dayOffset = (firstDay.getDay() - this.config.firstDayOfWeek + 7) % 7;
        startDate.setDate(startDate.getDate() - dayOffset);

        const weekdays = this.getWeekdayHeaders();
        const weeks = [];
        let currentDate = new Date(startDate);

        // Generate 6 weeks to ensure full month coverage
        for (let week = 0; week < 6; week++) {
            const days = [];
            for (let day = 0; day < 7; day++) {
                days.push(this.renderDay(new Date(currentDate), month));
                currentDate.setDate(currentDate.getDate() + 1);
            }
            weeks.push(`<tr role="row">${days.join('')}</tr>`);
        }

        return `
            <div class="adp-month" data-month="${month}" data-year="${year}">
                <table class="adp-month-table" role="grid">
                    <thead>
                        <tr role="row">
                            ${weekdays.map(day => `<th role="columnheader" scope="col">${day}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${weeks.join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    renderDay(date, currentMonth) {
        const dateStr = date.toISOString().split('T')[0];
        const isCurrentMonth = date.getMonth() === currentMonth;
        const isToday = this.isSameDay(date, new Date());
        const isSelected = this.isDateSelected(date);
        const isDisabled = this.isDateDisabled(date);
        const isFocused = this.isSameDay(date, this.state.focusedDate);
        const isInRange = this.isDateInRange(date);
        const hasTime = this.state.timeSlots.has(dateStr);

        const classes = [
            'adp-day',
            !isCurrentMonth && 'adp-day-other-month',
            isToday && 'adp-day-today',
            isSelected && 'adp-day-selected',
            isDisabled && 'adp-day-disabled',
            isFocused && 'adp-day-focused',
            isInRange && 'adp-day-in-range',
            hasTime && 'adp-day-has-time'
        ].filter(Boolean).join(' ');

        return `
            <td role="gridcell"
                class="${classes}"
                data-date="${dateStr}"
                aria-selected="${isSelected}"
                aria-disabled="${isDisabled}"
                tabindex="${isFocused ? '0' : '-1'}">
                <button type="button" class="adp-day-btn" ${isDisabled ? 'disabled' : ''}>
                    ${date.getDate()}
                    ${hasTime ? '<span class="adp-time-indicator">•</span>' : ''}
                </button>
            </td>
        `;
    }

    getWeekdayHeaders() {
        const weekdays = [];
        const baseDate = new Date(2023, 0, 1 + this.config.firstDayOfWeek); // Start from configured first day

        for (let i = 0; i < 7; i++) {
            const date = new Date(baseDate);
            date.setDate(date.getDate() + i);
            weekdays.push(date.toLocaleDateString(this.config.locale, { weekday: 'short' }));
        }

        return weekdays;
    }

    renderTimePanel() {
        if (this.state.selectedDates.length === 0) {
            return '<div class="adp-time-panel" style="display: none;"></div>';
        }

        const selectedDate = this.state.selectedDates[this.state.selectedDates.length - 1];
        const dateStr = selectedDate.toISOString().split('T')[0];
        const times = this.state.timeSlots.get(dateStr) || [];

        return `
            <div class="adp-time-panel">
                <div class="adp-time-header">
                    <h3>Times for ${selectedDate.toLocaleDateString(this.config.locale)}</h3>
                    <button class="adp-time-add-btn" type="button">+ Add Time</button>
                </div>
                <div class="adp-time-list">
                    ${times.map((time, index) => this.renderTimeSlot(time, index, dateStr)).join('')}
                </div>
                ${this.renderTimeInput()}
            </div>
        `;
    }

    renderTimeSlot(time, index, dateStr) {
        return `
            <div class="adp-time-slot" data-date="${dateStr}" data-index="${index}">
                <span class="adp-time-value">${this.formatTime(time)}</span>
                <button class="adp-time-remove-btn" type="button" aria-label="Remove time">×</button>
            </div>
        `;
    }

    renderTimeInput() {
        return `
            <div class="adp-time-input" style="display: none;">
                <div class="adp-time-controls">
                    <select class="adp-hour-select" aria-label="Hour">
                        ${this.renderHourOptions()}
                    </select>
                    <span class="adp-time-separator">:</span>
                    <select class="adp-minute-select" aria-label="Minute">
                        ${this.renderMinuteOptions()}
                    </select>
                    ${this.config.timeFormat === '12h' ? this.renderAmPmSelect() : ''}
                </div>
                <div class="adp-time-actions">
                    <button class="adp-time-save-btn" type="button">Save</button>
                    <button class="adp-time-cancel-btn" type="button">Cancel</button>
                </div>
            </div>
        `;
    }

    renderHourOptions() {
        const hours = this.config.timeFormat === '12h' ? 12 : 24;
        const start = this.config.timeFormat === '12h' ? 1 : 0;
        const options = [];

        for (let i = start; i <= hours; i++) {
            const value = this.config.timeFormat === '12h' ? i : i.toString().padStart(2, '0');
            options.push(`<option value="${value}">${value}</option>`);
        }

        return options.join('');
    }

    renderMinuteOptions() {
        const options = [];
        for (let i = 0; i < 60; i += this.config.minuteStep) {
            const value = i.toString().padStart(2, '0');
            options.push(`<option value="${value}">${value}</option>`);
        }
        return options.join('');
    }

    renderAmPmSelect() {
        return `
            <select class="adp-ampm-select" aria-label="AM/PM">
                <option value="AM">AM</option>
                <option value="PM">PM</option>
            </select>
        `;
    }

    renderFooter() {
        return `
            <div class="adp-footer">
                <div class="adp-actions">
                    <button class="adp-today-btn" type="button">Today</button>
                    <button class="adp-clear-btn" type="button">Clear</button>
                    ${this.config.mode === 'popup' ? '<button class="adp-close-btn" type="button">Close</button>' : ''}
                </div>
            </div>
        `;
    }

    // Utility methods for date operations
    isSameDay(date1, date2) {
        return date1.getFullYear() === date2.getFullYear() &&
            date1.getMonth() === date2.getMonth() &&
            date1.getDate() === date2.getDate();
    }

    isDateSelected(date) {
        return this.state.selectedDates.some(selectedDate => this.isSameDay(date, selectedDate));
    }

    isDateDisabled(date) {
        const dateStr = date.toISOString().split('T')[0];

        // Check if date is in disabled dates set
        if (this.state.disabledDates.has(dateStr)) {
            return true;
        }

        // Check min/max constraints
        if (this.config.min && date < this.config.min) {
            return true;
        }

        if (this.config.max && date > this.config.max) {
            return true;
        }

        return false;
    }

    isDateInRange(date) {
        if (this.config.selection !== 'range' || this.state.selectedDates.length !== 2) {
            return false;
        }

        const [start, end] = this.state.selectedDates.sort((a, b) => a - b);
        return date >= start && date <= end;
    }

    formatTime(timeStr) {
        if (this.config.timeFormat === '12h') {
            const [hours, minutes] = timeStr.split(':');
            const hour = parseInt(hours);
            const ampm = hour >= 12 ? 'PM' : 'AM';
            const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
            return `${displayHour}:${minutes} ${ampm}`;
        }
        return timeStr;
    }

    parseTimeInput(hour, minute, ampm = null) {
        let h = parseInt(hour);
        const m = parseInt(minute);

        if (this.config.timeFormat === '12h' && ampm) {
            if (ampm === 'PM' && h !== 12) h += 12;
            if (ampm === 'AM' && h === 12) h = 0;
        }

        return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
    }

    // Validation helper methods
    getDisabledReason(date) {
        const dateStr = date.toISOString().split('T')[0];

        if (this.state.disabledDates.has(dateStr)) {
            return 'Date is disabled';
        }

        if (this.config.min && date < this.config.min) {
            return `Date is before minimum allowed date (${this.config.min.toLocaleDateString(this.config.locale)})`;
        }

        if (this.config.max && date > this.config.max) {
            return `Date is after maximum allowed date (${this.config.max.toLocaleDateString(this.config.locale)})`;
        }

        return 'Date is not selectable';
    }

    showValidationError(message) {
        // Create or update error message
        let errorEl = this.shadowRoot.querySelector('.adp-error-message');
        if (!errorEl) {
            errorEl = document.createElement('div');
            errorEl.className = 'adp-error-message';
            errorEl.setAttribute('role', 'alert');
            errorEl.setAttribute('aria-live', 'polite');

            const picker = this.shadowRoot.querySelector('.adp-picker');
            picker.appendChild(errorEl);
        }

        errorEl.textContent = message;
        errorEl.style.display = 'block';

        // Auto-hide after 3 seconds
        setTimeout(() => {
            if (errorEl) {
                errorEl.style.display = 'none';
            }
        }, 3000);
    }

    validateSelection() {
        const errors = [];

        // Validate each selected date
        this.state.selectedDates.forEach(date => {
            if (this.isDateDisabled(date)) {
                errors.push({
                    date: date.toISOString().split('T')[0],
                    reason: this.getDisabledReason(date)
                });
            }
        });

        // Validate time slots
        this.state.timeSlots.forEach((times, dateStr) => {
            times.forEach(time => {
                const dateTime = new Date(`${dateStr}T${time}:00`);
                if (this.config.min && dateTime < this.config.min) {
                    errors.push({
                        date: dateStr,
                        time: time,
                        reason: 'Time is before minimum allowed date-time'
                    });
                }
                if (this.config.max && dateTime > this.config.max) {
                    errors.push({
                        date: dateStr,
                        time: time,
                        reason: 'Time is after maximum allowed date-time'
                    });
                }
            });
        });

        const isValid = errors.length === 0;
        this.dispatchEvent(new CustomEvent('validate', {
            detail: { valid: isValid, errors }
        }));

        return isValid;
    }

    getStyles() {
        return `<style>
            :host {
                --adp-bg: #ffffff;
                --adp-fg: #333333;
                --adp-accent: #007bff;
                --adp-accent-contrast: #ffffff;
                --adp-border: #e0e0e0;
                --adp-border-hover: #cccccc;
                --adp-radius: 6px;
                --adp-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                --adp-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
                --adp-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                --adp-font-size: 14px;
                --adp-spacing: 8px;
                --adp-spacing-sm: 4px;
                --adp-spacing-lg: 16px;

                /* Dark theme overrides */
                --adp-bg-dark: #2d2d2d;
                --adp-fg-dark: #ffffff;
                --adp-accent-dark: #4dabf7;
                --adp-border-dark: #404040;
                --adp-border-hover-dark: #555555;

                display: inline-block;
                font-family: var(--adp-font-family);
                font-size: var(--adp-font-size);
                line-height: 1.4;
            }

            .adp-container {
                position: relative;
            }

            .adp-container[data-theme="dark"] {
                --adp-bg: var(--adp-bg-dark);
                --adp-fg: var(--adp-fg-dark);
                --adp-accent: var(--adp-accent-dark);
                --adp-border: var(--adp-border-dark);
                --adp-border-hover: var(--adp-border-hover-dark);
            }

            /* Trigger button */
            .adp-trigger {
                display: flex;
                align-items: center;
                gap: var(--adp-spacing);
                padding: var(--adp-spacing) var(--adp-spacing-lg);
                background: var(--adp-bg);
                color: var(--adp-fg);
                border: 1px solid var(--adp-border);
                border-radius: var(--adp-radius);
                cursor: pointer;
                font-family: inherit;
                font-size: inherit;
                min-width: 200px;
                transition: border-color 0.2s ease;
            }

            .adp-trigger:hover {
                border-color: var(--adp-border-hover);
            }

            .adp-trigger:focus {
                outline: 2px solid var(--adp-accent);
                outline-offset: 2px;
            }

            .adp-trigger-text {
                flex: 1;
                text-align: left;
            }

            .adp-trigger-icon {
                opacity: 0.7;
            }

            /* Picker container */
            .adp-picker {
                background: var(--adp-bg);
                color: var(--adp-fg);
                border: 1px solid var(--adp-border);
                border-radius: var(--adp-radius);
                overflow: hidden;
            }

            .adp-popup {
                position: absolute;
                top: 100%;
                left: 0;
                z-index: 1000;
                box-shadow: var(--adp-shadow);
                margin-top: var(--adp-spacing-sm);
                min-width: 320px;
            }

            .adp-inline {
                box-shadow: var(--adp-shadow-light);
            }

            /* Header */
            .adp-header {
                padding: var(--adp-spacing-lg);
                border-bottom: 1px solid var(--adp-border);
            }

            .adp-nav {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: var(--adp-spacing-lg);
            }

            .adp-nav-btn {
                background: none;
                border: 1px solid var(--adp-border);
                color: var(--adp-fg);
                padding: var(--adp-spacing-sm) var(--adp-spacing);
                border-radius: var(--adp-radius);
                cursor: pointer;
                font-size: 16px;
                transition: all 0.2s ease;
            }

            .adp-nav-btn:hover {
                background: var(--adp-accent);
                color: var(--adp-accent-contrast);
                border-color: var(--adp-accent);
            }

            .adp-month-year {
                flex: 1;
                text-align: center;
                margin: 0 var(--adp-spacing-lg);
            }

            .adp-month-btn {
                background: none;
                border: none;
                color: var(--adp-fg);
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                padding: var(--adp-spacing-sm);
                border-radius: var(--adp-radius);
                transition: background-color 0.2s ease;
            }

            .adp-month-btn:hover {
                background: rgba(0, 0, 0, 0.05);
            }

            .adp-presets {
                display: flex;
                gap: var(--adp-spacing-sm);
                flex-wrap: wrap;
            }

            .adp-preset-btn {
                background: none;
                border: 1px solid var(--adp-border);
                color: var(--adp-fg);
                padding: var(--adp-spacing-sm) var(--adp-spacing);
                border-radius: var(--adp-radius);
                cursor: pointer;
                font-size: 12px;
                transition: all 0.2s ease;
            }

            .adp-preset-btn:hover {
                background: var(--adp-accent);
                color: var(--adp-accent-contrast);
                border-color: var(--adp-accent);
            }

            /* Calendar */
            .adp-calendar {
                padding: var(--adp-spacing-lg);
                display: grid;
                gap: var(--adp-spacing-lg);
            }

            .adp-month {
                min-width: 280px;
            }

            .adp-month-table {
                width: 100%;
                border-collapse: collapse;
                table-layout: fixed;
            }

            .adp-month-table th {
                padding: var(--adp-spacing);
                text-align: center;
                font-weight: 600;
                color: var(--adp-fg);
                opacity: 0.7;
                font-size: 12px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .adp-day {
                position: relative;
                padding: 2px;
            }

            .adp-day-btn {
                width: 100%;
                height: 36px;
                background: none;
                border: none;
                color: var(--adp-fg);
                cursor: pointer;
                border-radius: var(--adp-radius);
                font-size: inherit;
                font-family: inherit;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                transition: all 0.2s ease;
            }

            .adp-day-btn:hover {
                background: rgba(0, 123, 255, 0.1);
            }

            .adp-day-btn:focus {
                outline: 2px solid var(--adp-accent);
                outline-offset: -2px;
            }

            .adp-day-other-month .adp-day-btn {
                opacity: 0.3;
            }

            .adp-day-today .adp-day-btn {
                font-weight: 600;
                color: var(--adp-accent);
            }

            .adp-day-selected .adp-day-btn {
                background: var(--adp-accent);
                color: var(--adp-accent-contrast);
            }

            .adp-day-in-range .adp-day-btn {
                background: rgba(0, 123, 255, 0.2);
            }

            .adp-day-disabled .adp-day-btn {
                opacity: 0.3;
                cursor: not-allowed;
                background: none !important;
            }

            .adp-day-focused .adp-day-btn {
                box-shadow: 0 0 0 2px var(--adp-accent);
            }

            .adp-time-indicator {
                position: absolute;
                bottom: 2px;
                right: 2px;
                width: 4px;
                height: 4px;
                background: var(--adp-accent);
                border-radius: 50%;
                font-size: 8px;
            }

            .adp-day-selected .adp-time-indicator {
                background: var(--adp-accent-contrast);
            }

            /* Time Panel */
            .adp-time-panel {
                border-top: 1px solid var(--adp-border);
                padding: var(--adp-spacing-lg);
            }

            .adp-time-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: var(--adp-spacing-lg);
            }

            .adp-time-header h3 {
                margin: 0;
                font-size: 14px;
                font-weight: 600;
            }

            .adp-time-add-btn {
                background: var(--adp-accent);
                color: var(--adp-accent-contrast);
                border: none;
                padding: var(--adp-spacing-sm) var(--adp-spacing);
                border-radius: var(--adp-radius);
                cursor: pointer;
                font-size: 12px;
                transition: opacity 0.2s ease;
            }

            .adp-time-add-btn:hover {
                opacity: 0.9;
            }

            .adp-time-list {
                display: flex;
                flex-wrap: wrap;
                gap: var(--adp-spacing-sm);
                margin-bottom: var(--adp-spacing-lg);
            }

            .adp-time-slot {
                display: flex;
                align-items: center;
                gap: var(--adp-spacing-sm);
                background: rgba(0, 123, 255, 0.1);
                padding: var(--adp-spacing-sm) var(--adp-spacing);
                border-radius: var(--adp-radius);
                font-size: 12px;
            }

            .adp-time-remove-btn {
                background: none;
                border: none;
                color: var(--adp-fg);
                cursor: pointer;
                padding: 0;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0.7;
                transition: all 0.2s ease;
            }

            .adp-time-remove-btn:hover {
                background: rgba(255, 0, 0, 0.1);
                color: #ff0000;
                opacity: 1;
            }

            .adp-time-input {
                background: rgba(0, 0, 0, 0.02);
                border: 1px solid var(--adp-border);
                border-radius: var(--adp-radius);
                padding: var(--adp-spacing-lg);
            }

            .adp-time-controls {
                display: flex;
                align-items: center;
                gap: var(--adp-spacing-sm);
                margin-bottom: var(--adp-spacing-lg);
            }

            .adp-hour-select,
            .adp-minute-select,
            .adp-ampm-select {
                padding: var(--adp-spacing-sm);
                border: 1px solid var(--adp-border);
                border-radius: var(--adp-radius);
                background: var(--adp-bg);
                color: var(--adp-fg);
                font-family: inherit;
            }

            .adp-time-separator {
                font-weight: 600;
                font-size: 16px;
            }

            .adp-time-actions {
                display: flex;
                gap: var(--adp-spacing);
            }

            .adp-time-save-btn,
            .adp-time-cancel-btn {
                padding: var(--adp-spacing-sm) var(--adp-spacing-lg);
                border: 1px solid var(--adp-border);
                border-radius: var(--adp-radius);
                cursor: pointer;
                font-family: inherit;
                font-size: 12px;
                transition: all 0.2s ease;
            }

            .adp-time-save-btn {
                background: var(--adp-accent);
                color: var(--adp-accent-contrast);
                border-color: var(--adp-accent);
            }

            .adp-time-cancel-btn {
                background: var(--adp-bg);
                color: var(--adp-fg);
            }

            .adp-time-save-btn:hover,
            .adp-time-cancel-btn:hover {
                opacity: 0.9;
            }

            /* Footer */
            .adp-footer {
                border-top: 1px solid var(--adp-border);
                padding: var(--adp-spacing-lg);
            }

            .adp-actions {
                display: flex;
                gap: var(--adp-spacing);
                justify-content: flex-end;
            }

            .adp-today-btn,
            .adp-clear-btn,
            .adp-close-btn {
                padding: var(--adp-spacing-sm) var(--adp-spacing-lg);
                border: 1px solid var(--adp-border);
                border-radius: var(--adp-radius);
                cursor: pointer;
                font-family: inherit;
                font-size: 12px;
                transition: all 0.2s ease;
            }

            .adp-today-btn {
                background: var(--adp-accent);
                color: var(--adp-accent-contrast);
                border-color: var(--adp-accent);
            }

            .adp-clear-btn,
            .adp-close-btn {
                background: var(--adp-bg);
                color: var(--adp-fg);
            }

            .adp-today-btn:hover,
            .adp-clear-btn:hover,
            .adp-close-btn:hover {
                opacity: 0.9;
            }

            /* Multi-month layout */
            @media (min-width: 640px) {
                .adp-calendar {
                    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                }
            }

            /* Mobile responsive */
            @media (max-width: 639px) {
                .adp-popup {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: calc(100vw - 32px);
                    max-width: 400px;
                    max-height: calc(100vh - 32px);
                    overflow-y: auto;
                }

                .adp-nav {
                    flex-wrap: wrap;
                    gap: var(--adp-spacing-sm);
                }

                .adp-month-year {
                    order: -1;
                    width: 100%;
                    margin: 0 0 var(--adp-spacing-sm) 0;
                }

                .adp-presets {
                    justify-content: center;
                }

                .adp-time-controls {
                    flex-wrap: wrap;
                    justify-content: center;
                }

                .adp-actions {
                    justify-content: center;
                    flex-wrap: wrap;
                }
            }

            /* Focus styles for accessibility */
            .adp-picker *:focus {
                outline: 2px solid var(--adp-accent);
                outline-offset: 2px;
            }

            .adp-day-btn:focus {
                outline-offset: -2px;
            }

            /* Animation for popup */
            .adp-popup {
                animation: adp-fade-in 0.2s ease-out;
            }

            @keyframes adp-fade-in {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            /* High contrast mode support */
            @media (prefers-contrast: high) {
                :host {
                    --adp-border: #000000;
                    --adp-border-hover: #000000;
                }

                .adp-container[data-theme="dark"] {
                    --adp-border: #ffffff;
                    --adp-border-hover: #ffffff;
                }
            }

            /* Reduced motion support */
            @media (prefers-reduced-motion: reduce) {
                * {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            }

            /* Error message */
            .adp-error-message {
                display: none;
                background: #fee;
                color: #c33;
                border: 1px solid #fcc;
                border-radius: var(--adp-radius);
                padding: var(--adp-spacing);
                margin: var(--adp-spacing-lg);
                font-size: 12px;
                text-align: center;
            }

            .adp-container[data-theme="dark"] .adp-error-message {
                background: #4a1a1a;
                color: #ff6b6b;
                border-color: #6a2a2a;
            }
        </style>`;
    }

    // Public API methods
    open() {
        if (this.config.mode === 'popup' && !this.state.isOpen) {
            this.state.isOpen = true;
            this.render();

            // Focus management for accessibility
            requestAnimationFrame(() => {
                const focusedCell = this.shadowRoot.querySelector('.adp-day-focused .adp-day-btn');
                if (focusedCell) {
                    focusedCell.focus();
                } else {
                    const firstFocusable = this.shadowRoot.querySelector('.adp-picker button, .adp-picker select');
                    if (firstFocusable) {
                        firstFocusable.focus();
                    }
                }
            });

            this.dispatchEvent(new CustomEvent('open'));
        }
    }

    close() {
        if (this.config.mode === 'popup' && this.state.isOpen) {
            // Store reference to trigger for focus restoration
            const trigger = this.shadowRoot.querySelector('.adp-trigger');

            this.state.isOpen = false;
            this.render();

            // Restore focus to trigger
            if (trigger) {
                trigger.focus();
            }

            this.dispatchEvent(new CustomEvent('close'));
        }
    }

    toggle() {
        this.state.isOpen ? this.close() : this.open();
    }

    getValue() {
        return this.state.selectedDates.map(date => ({
            date: date.toISOString().split('T')[0],
            times: this.state.timeSlots.get(date.toISOString().split('T')[0]) || []
        }));
    }

    // Navigation methods
    navigateMonth(delta) {
        const newMonth = new Date(this.state.currentMonth);
        newMonth.setMonth(newMonth.getMonth() + delta);
        this.state.currentMonth = newMonth;
        this.render();
    }

    navigateYear(delta) {
        const newMonth = new Date(this.state.currentMonth);
        newMonth.setFullYear(newMonth.getFullYear() + delta);
        this.state.currentMonth = newMonth;
        this.render();
    }

    goToToday() {
        const today = new Date();
        this.state.currentMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        this.setFocusedDate(today);
    }

    // Time input methods
    saveTimeInput() {
        const timeInput = this.shadowRoot.querySelector('.adp-time-input');
        const hourSelect = timeInput.querySelector('.adp-hour-select');
        const minuteSelect = timeInput.querySelector('.adp-minute-select');
        const ampmSelect = timeInput.querySelector('.adp-ampm-select');

        const hour = hourSelect.value;
        const minute = minuteSelect.value;
        const ampm = ampmSelect?.value;

        const timeStr = this.parseTimeInput(hour, minute, ampm);

        // Add time to the last selected date
        if (this.state.selectedDates.length > 0) {
            const lastDate = this.state.selectedDates[this.state.selectedDates.length - 1];
            const dateStr = lastDate.toISOString().split('T')[0];

            const times = this.state.timeSlots.get(dateStr) || [];
            if (!times.includes(timeStr)) {
                times.push(timeStr);
                times.sort();
                this.state.timeSlots.set(dateStr, times);
            }
        }

        this.cancelTimeInput();
        this.render();
        this.dispatchEvent(new CustomEvent('change', { detail: this.getValue() }));
    }

    cancelTimeInput() {
        const timeInput = this.shadowRoot.querySelector('.adp-time-input');
        timeInput.style.display = 'none';
    }

    // Preset methods
    handlePreset(preset) {
        const today = new Date();

        switch (preset) {
            case 'today':
                this.state.selectedDates = [new Date(today)];
                break;
            case 'week':
                const endDate = new Date(today);
                endDate.setDate(endDate.getDate() + 6);
                if (this.config.selection === 'range') {
                    this.state.selectedDates = [new Date(today), endDate];
                } else if (this.config.selection === 'multiple') {
                    this.state.selectedDates = [];
                    for (let i = 0; i < 7; i++) {
                        const date = new Date(today);
                        date.setDate(date.getDate() + i);
                        this.state.selectedDates.push(date);
                    }
                }
                break;
            case 'month':
                const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                if (this.config.selection === 'range') {
                    this.state.selectedDates = [monthStart, monthEnd];
                }
                break;
        }

        this.render();
        this.dispatchEvent(new CustomEvent('change', { detail: this.getValue() }));
    }

    setValue(value) {
        if (!Array.isArray(value)) return;

        this.state.selectedDates = [];
        this.state.timeSlots.clear();

        value.forEach(item => {
            if (item.date) {
                const date = new Date(item.date + 'T00:00:00');
                this.state.selectedDates.push(date);

                if (item.times && item.times.length > 0) {
                    this.state.timeSlots.set(item.date, [...item.times]);
                }
            }
        });

        // Update current month to show first selected date
        if (this.state.selectedDates.length > 0) {
            const firstDate = this.state.selectedDates[0];
            this.state.currentMonth = new Date(firstDate.getFullYear(), firstDate.getMonth(), 1);
            this.state.focusedDate = new Date(firstDate);
        }

        this.render();
    }

    clear() {
        this.state.selectedDates = [];
        this.state.timeSlots.clear();
        this.render();
        this.dispatchEvent(new CustomEvent('change', { detail: this.getValue() }));
    }

    // Additional API methods
    disableDates(datesArray) {
        if (!Array.isArray(datesArray)) return;

        datesArray.forEach(dateStr => {
            this.state.disabledDates.add(dateStr);
        });

        this.render();
    }

    enableDates(datesArray) {
        if (!Array.isArray(datesArray)) return;

        datesArray.forEach(dateStr => {
            this.state.disabledDates.delete(dateStr);
        });

        this.render();
    }

    setMin(dateString) {
        this.config.min = dateString ? new Date(dateString) : null;
        this.render();
    }

    setMax(dateString) {
        this.config.max = dateString ? new Date(dateString) : null;
        this.render();
    }

    // Properties getters/setters
    get selectionMode() {
        return this.config.selection;
    }

    set selectionMode(value) {
        this.config.selection = value;
        this.render();
    }

    get locale() {
        return this.config.locale;
    }

    set locale(value) {
        this.config.locale = value;
        this.render();
    }

    get min() {
        return this.config.min;
    }

    set min(value) {
        this.config.min = value ? new Date(value) : null;
        this.render();
    }

    get max() {
        return this.config.max;
    }

    set max(value) {
        this.config.max = value ? new Date(value) : null;
        this.render();
    }

    get disabledDates() {
        return Array.from(this.state.disabledDates);
    }

    set disabledDates(value) {
        this.state.disabledDates = new Set(Array.isArray(value) ? value : []);
        this.render();
    }

    get months() {
        return this.config.months;
    }

    set months(value) {
        this.config.months = parseInt(value) || 1;
        this.render();
    }
}

// Register the custom element
customElements.define('advanced-date-picker', AdvancedDatePicker);

// Built-in demo when file is opened directly
if (typeof document !== 'undefined' && document.currentScript) {
    document.addEventListener('DOMContentLoaded', () => {
        // Only create demo if this is the main script (not imported as module)
        if (document.currentScript.src && !document.querySelector('advanced-date-picker')) {
            createDemo();
        }
    });
}

function createDemo() {
    const demoHTML = `
        <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px;">
            <h1>Advanced Date Picker Demo</h1>
            <p>A comprehensive date and time picker with multiple selection modes, accessibility features, and theming support.</p>

            <div style="display: grid; gap: 30px; margin-top: 30px;">
                <section>
                    <h2>Single Date Selection</h2>
                    <advanced-date-picker id="single" selection="single" locale="en-US"></advanced-date-picker>
                    <div id="single-output" style="margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 4px; font-family: monospace; font-size: 12px;"></div>
                </section>

                <section>
                    <h2>Date Range Selection</h2>
                    <advanced-date-picker id="range" selection="range" months="2" locale="en-US"></advanced-date-picker>
                    <div id="range-output" style="margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 4px; font-family: monospace; font-size: 12px;"></div>
                </section>

                <section>
                    <h2>Multiple Date Selection</h2>
                    <advanced-date-picker id="multiple" selection="multiple" locale="en-US"></advanced-date-picker>
                    <div id="multiple-output" style="margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 4px; font-family: monospace; font-size: 12px;"></div>
                </section>

                <section>
                    <h2>Inline Mode with Time Picker</h2>
                    <advanced-date-picker id="inline" mode="inline" selection="single" time-format="12h" minute-step="15"></advanced-date-picker>
                    <div id="inline-output" style="margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 4px; font-family: monospace; font-size: 12px;"></div>
                </section>

                <section>
                    <h2>Dark Theme</h2>
                    <advanced-date-picker id="dark" theme="dark" selection="range" locale="en-GB" first-day-of-week="1"></advanced-date-picker>
                    <div id="dark-output" style="margin-top: 10px; padding: 10px; background: #2d2d2d; color: white; border-radius: 4px; font-family: monospace; font-size: 12px;"></div>
                </section>

                <section>
                    <h2>With Constraints</h2>
                    <advanced-date-picker id="constrained" selection="single" min="2024-01-01" max="2024-12-31"></advanced-date-picker>
                    <div id="constrained-output" style="margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 4px; font-family: monospace; font-size: 12px;"></div>
                </section>
            </div>

            <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                <h2>Test Cases</h2>
                <p>The following test cases can be performed:</p>
                <ul style="line-height: 1.6;">
                    <li><strong>Keyboard Navigation:</strong> Use arrow keys to navigate dates, Enter/Space to select, Escape to close popup</li>
                    <li><strong>Range Selection:</strong> Click start date, then end date. Or click start and Shift+click end</li>
                    <li><strong>Multi-time:</strong> Select a date in inline mode, add multiple times using the time picker</li>
                    <li><strong>Disabled Dates:</strong> Try selecting dates outside min/max range in constrained picker</li>
                    <li><strong>Accessibility:</strong> Navigate using only keyboard, test with screen reader</li>
                    <li><strong>Responsive:</strong> Resize window to test mobile layout</li>
                </ul>
            </div>

            <div style="margin-top: 30px; padding: 20px; background: #e3f2fd; border-radius: 8px;">
                <h2>API Usage Examples</h2>
                <pre style="background: white; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px;"><code>// Basic usage
const picker = document.querySelector('advanced-date-picker');

// Listen for changes
picker.addEventListener('change', e => {
    console.log('Selection:', e.detail);
});

// Programmatic control
picker.setValue([
    { date: '2024-01-15', times: ['09:00', '14:30'] },
    { date: '2024-01-16', times: [] }
]);

// Get current value
const value = picker.getValue();

// Set constraints
picker.setMin('2024-01-01');
picker.setMax('2024-12-31');
picker.disableDates(['2024-01-25', '2024-02-14']);

// Properties
picker.selectionMode = 'range';
picker.locale = 'en-GB';
picker.months = 2;</code></pre>
            </div>
        </div>
    `;

    document.body.innerHTML = demoHTML;

    // Set up event listeners for demo
    ['single', 'range', 'multiple', 'inline', 'dark', 'constrained'].forEach(id => {
        const picker = document.getElementById(id);
        const output = document.getElementById(id + '-output');

        picker.addEventListener('change', e => {
            output.textContent = JSON.stringify(e.detail, null, 2);
        });

        picker.addEventListener('validate', e => {
            if (!e.detail.valid) {
                console.log('Validation failed:', e.detail.errors);
            }
        });
    });

    // Disable some dates for the constrained picker
    const constrainedPicker = document.getElementById('constrained');
    constrainedPicker.disableDates(['2024-12-25', '2024-01-01']);
}

// Export for module usage
export default AdvancedDatePicker;
